<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 阅读分享平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" th:href="@{/}">
                <i class="fas fa-book-reader me-2"></i>阅读分享
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" th:href="@{/}">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/books}">图书</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/notes/public}">公开笔记</a>
                    </li>
                </ul>
                
                <!-- 搜索框 -->
                <form class="d-flex me-3" th:action="@{/search}" method="get">
                    <input class="form-control me-2" type="search" name="keyword" placeholder="搜索图书或笔记..." style="width: 200px;">
                    <button class="btn btn-outline-light" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
                
                <!-- 用户菜单 -->
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/login}">登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/register}">注册</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <main>
        <!-- Hero Section -->
        <section class="bg-primary text-white py-5">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <h1 class="display-4 fw-bold mb-3">分享阅读心得</h1>
                        <p class="lead mb-4">记录读书感悟，分享知识见解，与志同道合的朋友一起成长</p>
                        <div class="d-grid gap-2 d-md-flex">
                            <a th:href="@{/register}" class="btn btn-light btn-lg me-md-2">立即注册</a>
                            <a th:href="@{/books}" class="btn btn-outline-light btn-lg">浏览图书</a>
                        </div>
                    </div>
                    <div class="col-lg-6 text-center">
                        <i class="fas fa-book-open" style="font-size: 8rem; opacity: 0.8;"></i>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 特色功能 -->
        <section class="py-5">
            <div class="container">
                <div class="row text-center mb-5">
                    <div class="col">
                        <h2 class="fw-bold">平台特色</h2>
                        <p class="text-muted">为读书爱好者打造的专业分享平台</p>
                    </div>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center p-4">
                                <div class="mb-3">
                                    <i class="fas fa-book text-primary" style="font-size: 3rem;"></i>
                                </div>
                                <h5 class="card-title">丰富图书库</h5>
                                <p class="card-text text-muted">收录各类优质图书，涵盖技术、文学、历史等多个领域</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center p-4">
                                <div class="mb-3">
                                    <i class="fas fa-edit text-primary" style="font-size: 3rem;"></i>
                                </div>
                                <h5 class="card-title">写作分享</h5>
                                <p class="card-text text-muted">记录读书笔记，分享阅读感悟，与他人交流心得体会</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center p-4">
                                <div class="mb-3">
                                    <i class="fas fa-users text-primary" style="font-size: 3rem;"></i>
                                </div>
                                <h5 class="card-title">社区互动</h5>
                                <p class="card-text text-muted">评论交流，建立读书社群，结识志同道合的朋友</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 最新图书 -->
        <section class="py-5 bg-light" th:if="${latestBooks != null and !latestBooks.isEmpty()}">
            <div class="container">
                <div class="row mb-4">
                    <div class="col">
                        <h3 class="fw-bold">最新图书</h3>
                        <p class="text-muted">发现更多优质读物</p>
                    </div>
                    <div class="col-auto">
                        <a th:href="@{/books}" class="btn btn-outline-primary">查看更多</a>
                    </div>
                </div>
                
                <div class="row g-4">
                    <div class="col-lg-2 col-md-3 col-sm-4 col-6" th:each="book : ${latestBooks}">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body p-3">
                                <div class="text-center mb-2">
                                    <i class="fas fa-book text-primary" style="font-size: 2rem;"></i>
                                </div>
                                <h6 class="card-title text-truncate" th:text="${book.title}">书名</h6>
                                <p class="card-text text-muted small text-truncate" th:text="${book.author}">作者</p>
                                <a th:href="@{/books/{id}(id=${book.id})}" class="btn btn-sm btn-outline-primary w-100">查看详情</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 最新笔记 -->
        <section class="py-5" th:if="${latestNotes != null and !latestNotes.isEmpty()}">
            <div class="container">
                <div class="row mb-4">
                    <div class="col">
                        <h3 class="fw-bold">最新笔记</h3>
                        <p class="text-muted">阅读他人的精彩分享</p>
                    </div>
                    <div class="col-auto">
                        <a th:href="@{/notes/public}" class="btn btn-outline-primary">查看更多</a>
                    </div>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-6 col-lg-4" th:each="note : ${latestNotes}">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                <h6 class="card-title" th:text="${note.title}">笔记标题</h6>
                                <p class="card-text text-muted small">
                                    <i class="fas fa-user me-1"></i><span th:text="${note.user?.nickname ?: note.user?.username}">作者</span>
                                    <span class="ms-2">
                                        <i class="fas fa-book me-1"></i><span th:text="${note.book?.title}">图书</span>
                                    </span>
                                </p>
                                <p class="card-text" th:text="${#strings.abbreviate(note.content, 100)}">笔记内容摘要...</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted" th:text="${#temporals.format(note.createTime, 'yyyy-MM-dd')}">日期</small>
                                    <a th:href="@{/notes/{id}(id=${note.id})}" class="btn btn-sm btn-outline-primary">阅读</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>阅读分享平台</h5>
                    <p class="text-muted">分享阅读心得，传递知识力量</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted">
                        &copy; 2024 阅读分享平台. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
