package com.coding24h.reading_share.config;

import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.service.RoleService;
import com.coding24h.reading_share.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 数据初始化器
 */
@Component
public class DataInitializer implements CommandLineRunner {
    
    @Autowired
    private RoleService roleService;
    
    @Autowired
    private UserService userService;
    
    @Override
    public void run(String... args) throws Exception {
        // 初始化默认角色
        roleService.initDefaultRoles();
        
        // 创建默认管理员用户
        if (!userService.existsByUsername("admin")) {
            User admin = new User();
            admin.setUsername("admin");
            admin.setPassword("admin123");
            admin.setEmail("<EMAIL>");
            admin.setNickname("管理员");
            admin.setStatus(1);
            
            User savedAdmin = userService.save(admin);
            // 分配管理员角色
            userService.assignRole(savedAdmin.getId(), 1L); // 假设角色ID 1 是管理员
        }
    }
}
