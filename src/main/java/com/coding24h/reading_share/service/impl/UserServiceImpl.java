package com.coding24h.reading_share.service.impl;

import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.entity.UserRole;
import com.coding24h.reading_share.mapper.UserMapper;
import com.coding24h.reading_share.mapper.UserRoleMapper;
import com.coding24h.reading_share.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现类
 */
@Service
@Transactional
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private UserRoleMapper userRoleMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    public User findById(Long id) {
        return userMapper.selectById(id);
    }
    
    @Override
    public User findByUsername(String username) {
        return userMapper.selectByUsername(username);
    }
    
    @Override
    public User findByEmail(String email) {
        return userMapper.selectByEmail(email);
    }
    
    @Override
    public List<User> findAll() {
        return userMapper.selectAll();
    }
    
    @Override
    public List<User> findByPage(int page, int size) {
        int offset = (page - 1) * size;
        return userMapper.selectByPage(offset, size);
    }
    
    @Override
    public int count() {
        return userMapper.selectCount();
    }
    
    @Override
    public User save(User user) {
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        if (user.getStatus() == null) {
            user.setStatus(1); // 默认启用
        }
        // 加密密码
        if (user.getPassword() != null) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        userMapper.insert(user);
        return user;
    }
    
    @Override
    public User update(User user) {
        user.setUpdateTime(LocalDateTime.now());
        // 如果密码不为空，则加密
        if (user.getPassword() != null && !user.getPassword().isEmpty()) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        userMapper.update(user);
        return user;
    }
    
    @Override
    public void deleteById(Long id) {
        // 先删除用户角色关联
        userRoleMapper.deleteByUserId(id);
        // 再删除用户
        userMapper.deleteById(id);
    }
    
    @Override
    public List<User> findByUsernameLike(String username) {
        return userMapper.selectByUsernameLike(username);
    }
    
    @Override
    public User register(User user) {
        // 检查用户名和邮箱是否已存在
        if (existsByUsername(user.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        if (existsByEmail(user.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 设置默认值
        if (user.getNickname() == null || user.getNickname().isEmpty()) {
            user.setNickname(user.getUsername());
        }
        
        // 保存用户
        User savedUser = save(user);
        
        // 分配默认角色（普通用户）
        assignRole(savedUser.getId(), 2L); // 假设角色ID 2 是普通用户
        
        return savedUser;
    }
    
    @Override
    public boolean existsByUsername(String username) {
        return userMapper.selectByUsername(username) != null;
    }
    
    @Override
    public boolean existsByEmail(String email) {
        return userMapper.selectByEmail(email) != null;
    }
    
    @Override
    public void assignRole(Long userId, Long roleId) {
        UserRole userRole = new UserRole();
        userRole.setUserId(userId);
        userRole.setRoleId(roleId);
        userRole.setCreateTime(LocalDateTime.now());
        userRoleMapper.insert(userRole);
    }
    
    @Override
    public void removeRole(Long userId, Long roleId) {
        userRoleMapper.deleteByUserIdAndRoleId(userId, roleId);
    }
}
