package com.coding24h.reading_share.service;

import com.coding24h.reading_share.entity.ReadingNote;

import java.util.List;

/**
 * 读书笔记服务接口
 */
public interface ReadingNoteService {
    
    /**
     * 根据ID查询读书笔记
     */
    ReadingNote findById(Long id);
    
    /**
     * 查询所有公开的读书笔记
     */
    List<ReadingNote> findAllPublic();
    
    /**
     * 根据用户ID查询读书笔记
     */
    List<ReadingNote> findByUserId(Long userId);
    
    /**
     * 根据图书ID查询读书笔记
     */
    List<ReadingNote> findByBookId(Long bookId);
    
    /**
     * 分页查询公开的读书笔记
     */
    List<ReadingNote> findPublicByPage(int page, int size);
    
    /**
     * 查询公开读书笔记总数
     */
    int countPublic();
    
    /**
     * 根据标题模糊查询
     */
    List<ReadingNote> findByTitleLike(String title);
    
    /**
     * 根据标签查询
     */
    List<ReadingNote> findByTags(String tags);
    
    /**
     * 保存读书笔记
     */
    ReadingNote save(ReadingNote readingNote);
    
    /**
     * 更新读书笔记
     */
    ReadingNote update(ReadingNote readingNote);
    
    /**
     * 删除读书笔记
     */
    void deleteById(Long id);
    
    /**
     * 根据用户ID和图书ID查询读书笔记
     */
    List<ReadingNote> findByUserIdAndBookId(Long userId, Long bookId);
    
    /**
     * 搜索读书笔记（综合搜索）
     */
    List<ReadingNote> search(String keyword);
    
    /**
     * 检查用户是否有权限访问笔记
     */
    boolean hasPermission(Long noteId, Long userId);
    
    /**
     * 发布笔记
     */
    void publish(Long noteId);
    
    /**
     * 取消发布笔记
     */
    void unpublish(Long noteId);
}
