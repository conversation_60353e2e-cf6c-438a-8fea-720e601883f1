package com.coding24h.reading_share.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 读书笔记实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReadingNote {
    private Long id;
    private Long userId;
    private Long bookId;
    private String title;
    private String content;
    private String tags;
    private Integer rating; // 评分 1-5星
    private Integer isPublic; // 0-私有 1-公开
    private Integer status; // 0-草稿 1-发布
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    // 关联对象
    private User user;
    private Book book;
}
