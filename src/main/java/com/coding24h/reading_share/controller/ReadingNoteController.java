package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.Book;
import com.coding24h.reading_share.entity.ReadingNote;
import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.service.BookService;
import com.coding24h.reading_share.service.ReadingNoteService;
import com.coding24h.reading_share.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;

/**
 * 读书笔记控制器
 */
@Controller
@RequestMapping("/notes")
public class ReadingNoteController {
    
    @Autowired
    private ReadingNoteService readingNoteService;
    
    @Autowired
    private BookService bookService;
    
    @Autowired
    private UserService userService;
    
    @GetMapping("/public")
    public String publicNotes(@RequestParam(defaultValue = "1") int page,
                             @RequestParam(defaultValue = "10") int size,
                             Model model) {
        List<ReadingNote> notes = readingNoteService.findPublicByPage(page, size);
        int totalCount = readingNoteService.countPublic();
        int totalPages = (int) Math.ceil((double) totalCount / size);
        
        model.addAttribute("notes", notes);
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", totalPages);
        model.addAttribute("totalCount", totalCount);
        
        return "notes/public-list";
    }
    
    @GetMapping("/my")
    public String myNotes(Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }
        
        List<ReadingNote> notes = readingNoteService.findByUserId(currentUser.getId());
        model.addAttribute("notes", notes);
        
        return "notes/my-list";
    }
    
    @GetMapping("/{id}")
    public String detail(@PathVariable Long id, Model model) {
        ReadingNote note = readingNoteService.findById(id);
        if (note == null) {
            return "error/404";
        }
        
        // 检查权限
        User currentUser = getCurrentUser();
        if (!readingNoteService.hasPermission(id, currentUser != null ? currentUser.getId() : null)) {
            return "error/403";
        }
        
        model.addAttribute("note", note);
        
        return "notes/detail";
    }
    
    @GetMapping("/add")
    public String add(Model model) {
        model.addAttribute("note", new ReadingNote());
        
        // 获取所有图书供选择
        List<Book> books = bookService.findAll();
        model.addAttribute("books", books);
        
        return "notes/form";
    }
    
    @PostMapping("/add")
    public String add(@ModelAttribute("note") ReadingNote note, 
                     BindingResult result, 
                     RedirectAttributes redirectAttributes,
                     Model model) {
        
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }
        
        // 设置用户ID
        note.setUserId(currentUser.getId());
        
        // 基本验证
        if (note.getTitle() == null || note.getTitle().trim().isEmpty()) {
            result.rejectValue("title", "error.note", "标题不能为空");
        }
        
        if (note.getContent() == null || note.getContent().trim().isEmpty()) {
            result.rejectValue("content", "error.note", "内容不能为空");
        }
        
        if (note.getBookId() == null) {
            result.rejectValue("bookId", "error.note", "请选择图书");
        }
        
        if (result.hasErrors()) {
            List<Book> books = bookService.findAll();
            model.addAttribute("books", books);
            return "notes/form";
        }
        
        try {
            readingNoteService.save(note);
            redirectAttributes.addFlashAttribute("success", "读书笔记添加成功");
            return "redirect:/notes/my";
        } catch (Exception e) {
            result.rejectValue("title", "error.note", "添加失败：" + e.getMessage());
            List<Book> books = bookService.findAll();
            model.addAttribute("books", books);
            return "notes/form";
        }
    }
    
    @GetMapping("/{id}/edit")
    public String edit(@PathVariable Long id, Model model) {
        ReadingNote note = readingNoteService.findById(id);
        if (note == null) {
            return "error/404";
        }
        
        User currentUser = getCurrentUser();
        if (currentUser == null || !note.getUserId().equals(currentUser.getId())) {
            return "error/403";
        }
        
        model.addAttribute("note", note);
        
        // 获取所有图书供选择
        List<Book> books = bookService.findAll();
        model.addAttribute("books", books);
        
        return "notes/form";
    }
    
    @PostMapping("/{id}/edit")
    public String edit(@PathVariable Long id,
                      @ModelAttribute("note") ReadingNote note, 
                      BindingResult result, 
                      RedirectAttributes redirectAttributes,
                      Model model) {
        
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }
        
        ReadingNote existingNote = readingNoteService.findById(id);
        if (existingNote == null || !existingNote.getUserId().equals(currentUser.getId())) {
            return "error/403";
        }
        
        note.setId(id);
        note.setUserId(currentUser.getId());
        
        // 基本验证
        if (note.getTitle() == null || note.getTitle().trim().isEmpty()) {
            result.rejectValue("title", "error.note", "标题不能为空");
        }
        
        if (note.getContent() == null || note.getContent().trim().isEmpty()) {
            result.rejectValue("content", "error.note", "内容不能为空");
        }
        
        if (note.getBookId() == null) {
            result.rejectValue("bookId", "error.note", "请选择图书");
        }
        
        if (result.hasErrors()) {
            List<Book> books = bookService.findAll();
            model.addAttribute("books", books);
            return "notes/form";
        }
        
        try {
            readingNoteService.update(note);
            redirectAttributes.addFlashAttribute("success", "读书笔记更新成功");
            return "redirect:/notes/" + id;
        } catch (Exception e) {
            result.rejectValue("title", "error.note", "更新失败：" + e.getMessage());
            List<Book> books = bookService.findAll();
            model.addAttribute("books", books);
            return "notes/form";
        }
    }
    
    @PostMapping("/{id}/delete")
    public String delete(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }
        
        ReadingNote note = readingNoteService.findById(id);
        if (note == null || !note.getUserId().equals(currentUser.getId())) {
            return "error/403";
        }
        
        try {
            readingNoteService.deleteById(id);
            redirectAttributes.addFlashAttribute("success", "读书笔记删除成功");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "删除失败：" + e.getMessage());
        }
        
        return "redirect:/notes/my";
    }
    
    @PostMapping("/{id}/publish")
    public String publish(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }
        
        ReadingNote note = readingNoteService.findById(id);
        if (note == null || !note.getUserId().equals(currentUser.getId())) {
            return "error/403";
        }
        
        try {
            readingNoteService.publish(id);
            redirectAttributes.addFlashAttribute("success", "读书笔记发布成功");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "发布失败：" + e.getMessage());
        }
        
        return "redirect:/notes/" + id;
    }
    
    @PostMapping("/{id}/unpublish")
    public String unpublish(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }
        
        ReadingNote note = readingNoteService.findById(id);
        if (note == null || !note.getUserId().equals(currentUser.getId())) {
            return "error/403";
        }
        
        try {
            readingNoteService.unpublish(id);
            redirectAttributes.addFlashAttribute("success", "读书笔记取消发布成功");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "取消发布失败：" + e.getMessage());
        }
        
        return "redirect:/notes/" + id;
    }
    
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && 
            !authentication.getName().equals("anonymousUser")) {
            return userService.findByUsername(authentication.getName());
        }
        return null;
    }
}
