package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.Book;
import com.coding24h.reading_share.entity.ReadingNote;
import com.coding24h.reading_share.service.BookService;
import com.coding24h.reading_share.service.ReadingNoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 首页控制器
 */
@Controller
public class HomeController {
    
    @Autowired
    private BookService bookService;
    
    @Autowired
    private ReadingNoteService readingNoteService;
    
    @GetMapping({"/", "/home"})
    public String home(Model model) {
        // 获取最新的图书
        List<Book> latestBooks = bookService.findByPage(1, 6);
        model.addAttribute("latestBooks", latestBooks);
        
        // 获取最新的公开读书笔记
        List<ReadingNote> latestNotes = readingNoteService.findPublicByPage(1, 6);
        model.addAttribute("latestNotes", latestNotes);
        
        return "index";
    }
    
    @GetMapping("/search")
    public String search(@RequestParam("keyword") String keyword, Model model) {
        // 搜索图书
        List<Book> books = bookService.search(keyword);
        model.addAttribute("books", books);
        
        // 搜索读书笔记
        List<ReadingNote> notes = readingNoteService.search(keyword);
        model.addAttribute("notes", notes);
        
        model.addAttribute("keyword", keyword);
        
        return "search-results";
    }
    
    @GetMapping("/about")
    public String about() {
        return "about";
    }
}
