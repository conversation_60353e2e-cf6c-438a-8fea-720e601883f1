package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.UserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户角色关联数据访问层
 */
@Mapper
public interface UserRoleMapper {
    
    /**
     * 根据用户ID查询用户角色关联
     */
    List<UserRole> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据角色ID查询用户角色关联
     */
    List<UserRole> selectByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 插入用户角色关联
     */
    int insert(UserRole userRole);
    
    /**
     * 删除用户角色关联
     */
    int deleteByUserIdAndRoleId(@Param("userId") Long userId, @Param("roleId") Long roleId);
    
    /**
     * 根据用户ID删除所有角色关联
     */
    int deleteByUserId(@Param("userId") Long userId);
    
    /**
     * 根据角色ID删除所有用户关联
     */
    int deleteByRoleId(@Param("roleId") Long roleId);
}
