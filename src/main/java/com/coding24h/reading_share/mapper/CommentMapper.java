package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.Comment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 评论数据访问层
 */
@Mapper
public interface CommentMapper {
    
    /**
     * 根据ID查询评论
     */
    Comment selectById(@Param("id") Long id);
    
    /**
     * 根据笔记ID查询评论
     */
    List<Comment> selectByNoteId(@Param("noteId") Long noteId);
    
    /**
     * 根据用户ID查询评论
     */
    List<Comment> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据父评论ID查询回复
     */
    List<Comment> selectByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查询所有评论
     */
    List<Comment> selectAll();
    
    /**
     * 分页查询评论
     */
    List<Comment> selectByPage(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 查询评论总数
     */
    int selectCount();
    
    /**
     * 插入评论
     */
    int insert(Comment comment);
    
    /**
     * 更新评论
     */
    int update(Comment comment);
    
    /**
     * 删除评论
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据笔记ID删除所有评论
     */
    int deleteByNoteId(@Param("noteId") Long noteId);
}
