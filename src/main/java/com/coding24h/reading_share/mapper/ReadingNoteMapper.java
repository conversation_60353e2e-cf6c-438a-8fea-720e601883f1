package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.ReadingNote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 读书笔记数据访问层
 */
@Mapper
public interface ReadingNoteMapper {
    
    /**
     * 根据ID查询读书笔记
     */
    ReadingNote selectById(@Param("id") Long id);
    
    /**
     * 查询所有公开的读书笔记
     */
    List<ReadingNote> selectAllPublic();
    
    /**
     * 根据用户ID查询读书笔记
     */
    List<ReadingNote> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据图书ID查询读书笔记
     */
    List<ReadingNote> selectByBookId(@Param("bookId") Long bookId);
    
    /**
     * 分页查询公开的读书笔记
     */
    List<ReadingNote> selectPublicByPage(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 查询公开读书笔记总数
     */
    int selectPublicCount();
    
    /**
     * 根据标题模糊查询
     */
    List<ReadingNote> selectByTitleLike(@Param("title") String title);
    
    /**
     * 根据标签查询
     */
    List<ReadingNote> selectByTags(@Param("tags") String tags);
    
    /**
     * 插入读书笔记
     */
    int insert(ReadingNote readingNote);
    
    /**
     * 更新读书笔记
     */
    int update(ReadingNote readingNote);
    
    /**
     * 删除读书笔记
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据用户ID和图书ID查询读书笔记
     */
    List<ReadingNote> selectByUserIdAndBookId(@Param("userId") Long userId, @Param("bookId") Long bookId);
}
