package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色数据访问层
 */
@Mapper
public interface RoleMapper {
    
    /**
     * 根据ID查询角色
     */
    Role selectById(@Param("id") Long id);
    
    /**
     * 根据角色代码查询角色
     */
    Role selectByRoleCode(@Param("roleCode") String roleCode);
    
    /**
     * 查询所有角色
     */
    List<Role> selectAll();
    
    /**
     * 根据用户ID查询角色列表
     */
    List<Role> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 插入角色
     */
    int insert(Role role);
    
    /**
     * 更新角色
     */
    int update(Role role);
    
    /**
     * 删除角色
     */
    int deleteById(@Param("id") Long id);
}
