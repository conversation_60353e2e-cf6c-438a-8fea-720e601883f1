package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.Book;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图书数据访问层
 */
@Mapper
public interface BookMapper {
    
    /**
     * 根据ID查询图书
     */
    Book selectById(@Param("id") Long id);
    
    /**
     * 查询所有图书
     */
    List<Book> selectAll();
    
    /**
     * 分页查询图书
     */
    List<Book> selectByPage(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 查询图书总数
     */
    int selectCount();
    
    /**
     * 根据标题模糊查询
     */
    List<Book> selectByTitleLike(@Param("title") String title);
    
    /**
     * 根据作者模糊查询
     */
    List<Book> selectByAuthorLike(@Param("author") String author);
    
    /**
     * 根据分类查询
     */
    List<Book> selectByCategory(@Param("category") String category);
    
    /**
     * 插入图书
     */
    int insert(Book book);
    
    /**
     * 更新图书
     */
    int update(Book book);
    
    /**
     * 删除图书
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据ISBN查询图书
     */
    Book selectByIsbn(@Param("isbn") String isbn);
}
